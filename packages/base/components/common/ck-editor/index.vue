<script setup lang="ts">
import {
  Alignment,
  AutoImage,
  Autoformat,
  Autosave,
  BlockQuote,
  Bold,
  Essentials,
  FontBackgroundColor,
  FontColor,
  FontFamily,
  FontSize,
  Heading,
  ImageBlock,
  ImageCaption,
  ImageInline,
  ImageInsert,
  ImageInsertViaUrl,
  ImageResize,
  ImageStyle,
  ImageTextAlternative,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  InlineEditor,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  MediaEmbed,
  Mention,
  Paragraph,
  PasteFromOffice,
  SimpleUploadAdapter,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  Subscript,
  Superscript,
  Table,
  TableCaption,
  TableColumnResize,
  TableToolbar,
  TextTransformation,
  TodoList,
  Underline,
} from 'ckeditor5'
import { Ckeditor } from '@ckeditor/ckeditor5-vue'
import 'ckeditor5/ckeditor5.css'
// @ts-expect-error - CKEditor5 translation import issue
import translations from 'ckeditor5/translations/zh-cn.js'
import { HuaweiOBSUploader, nanoid } from '@sa/utils'
import { Mathlive, MathlivePanelview } from './components/plugins'

interface Props {
  /** 编辑器最小高度，默认32px */
  minHeight?: number
  /** 编辑器占位符文本 */
  placeholder?: string
  /** 是否禁用编辑器 */
  disabled?: boolean
  /** 工具栏配置 */
  toolbarItems?: string[]
  /** 是否显示数学公式工具 */
  enableMath?: boolean
}

defineOptions({
  name: 'CKEditor',
})

const props = withDefaults(defineProps<Props>(), {
  minHeight: 32,
  placeholder: '请输入内容...',
  disabled: false,
  toolbarItems: () => [
    'fontFamily',
    'fontSize',
    'fontColor',
    'fontBackgroundColor',
    '|',
    'bold',
    'italic',
    'underline',
    'strikethrough',
    'subscript',
    'superscript',
    '|',
    'alignment',
    'outdent',
    'indent',
    '|',
    'insertImage',
    'insertTable',
    'specialCharacters',
    'mathlive',
  ],
  enableMath: true,
})

const editorValue = defineModel<string>('editorValue', {
  default: '',
})

// 常量定义
const LICENSE_KEY = 'GPL'
const editor = InlineEditor

// 响应式状态
const isLayoutReady = ref(false)
const editorInstance = ref<any>(null)
const isEditorReady = ref(false)

// 确保编辑器实例的唯一性
const editorId = `ckeditor-${nanoid()}`

// 基础插件配置
function getBasePlugins() {
  return [
    Essentials,
    Autoformat,
    Bold,
    Italic,
    Underline,
    Strikethrough,
    Subscript,
    Superscript,
    Paragraph,
    FontFamily,
    FontSize,
    FontColor,
    FontBackgroundColor,
    Alignment,
    Indent,
    IndentBlock,
    Link,
    List,
    ListProperties,
    PasteFromOffice,
    TextTransformation,
  ]
}

// 图片相关插件
function getImagePlugins() {
  return [
    AutoImage,
    ImageBlock,
    ImageCaption,
    ImageInline,
    ImageInsert,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageTextAlternative,
    ImageToolbar,
    ImageUpload,
    LinkImage,
    SimpleUploadAdapter,
  ]
}

// 表格相关插件
function getTablePlugins() {
  return [
    Table,
    TableCaption,
    TableColumnResize,
    TableToolbar,
  ]
}

// 特殊字符和其他插件
function getSpecialPlugins() {
  return [
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    TodoList,
    BlockQuote,
    Heading,
    MediaEmbed,
    Mention,
    Autosave,
  ]
}

// 数学公式插件
const getMathPlugins = () => props.enableMath ? [Mathlive, MathlivePanelview] : []

// 获取所有插件
function getAllPlugins() {
  return [
    ...getBasePlugins(),
    ...getImagePlugins(),
    ...getTablePlugins(),
    ...getSpecialPlugins(),
    ...getMathPlugins(),
  ]
}

// 工具栏配置
function getToolbarConfig() {
  return {
    items: props.toolbarItems.filter((item) => {
    // 如果禁用数学功能，过滤掉数学工具
      if (!props.enableMath && item === 'mathlive') {
        return false
      }
      return true
    }),
    shouldNotGroupWhenFull: true,
  }
}

// 数学公式配置
function getMathConfig() {
  return {
    openPanelWhenEquationSelected: true,
    renderMathPanel: (element: HTMLElement) => {
      let panelView: MathlivePanelview | null = new MathlivePanelview()
      panelView.mount(element)
      return () => {
        panelView?.destroy()
        panelView = null
      }
    },
    output: {
      type: 'span',
      attributes: {
        class: 'math-tex',
      },
    },
  }
}

const config = computed(() => {
  if (!isLayoutReady.value) {
    return null
  }

  return {
    toolbar: getToolbarConfig(),
    plugins: getAllPlugins(),
    fontFamily: {
      supportAllValues: true,
      options: [
        'default',
        '宋体, SimSun, serif',
        '黑体, SimHei, sans-serif',
        '微软雅黑, Microsoft YaHei, sans-serif',
        '楷体, KaiTi, serif',
        '仿宋, FangSong, serif',
        '华文宋体, STSong, serif',
        '华文黑体, STHeiti, sans-serif',
        '华文楷体, STKaiti, serif',
        '华文仿宋, STFangsong, serif',
      ],
    },
    ...(props.enableMath && { mathlive: getMathConfig() }),
    image: {
      toolbar: [
        'toggleImageCaption',
        'imageTextAlternative',
        '|',
        'imageStyle:inline',
        'imageStyle:wrapText',
        'imageStyle:breakText',
        '|',
        'resizeImage',
      ],
    },
    initialData: '',
    language: 'zh-cn',
    licenseKey: LICENSE_KEY,
    list: {
      properties: {
        styles: true,
        startIndex: true,
        reversed: true,
      },
    },
    placeholder: props.placeholder,
    table: {
      contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells'],
    },
    translations: [translations],
  }
})

// 自定义上传适配器
function createCustomUploadAdapter(loader: any) {
  return {
    upload() {
      return loader.file.then((file: File) => {
        return new Promise((resolve, reject) => {
          const uploader = new HuaweiOBSUploader({})

          uploader.upload({
            file,
            onProgress: (percent: number) => {
              // CKEditor 上传进度回调
              loader.uploadTotal = 100
              loader.uploaded = percent
            },
          }).then((result) => {
            resolve({
              default: result.url,
            })
          }).catch((error) => {
            reject(error)
          })
        })
      })
    },
    abort() {
      // 取消上传的逻辑
    },
  }
}

// 编辑器事件处理
function handleEditorReady(editor: any) {
  editorInstance.value = editor
  isEditorReady.value = true

  // 注册自定义上传适配器
  editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
    return createCustomUploadAdapter(loader)
  }

  // 如果编辑器被禁用，设置为只读模式
  if (props.disabled) {
    editor.enableReadOnlyMode('disabled')
  }
}

function handleEditorError(error: Error) {
  console.error('CKEditor initialization failed:', error)
}

// 监听禁用状态变化
watch(() => props.disabled, (newDisabled) => {
  if (editorInstance.value) {
    if (newDisabled) {
      editorInstance.value.enableReadOnlyMode('disabled')
    }
    else {
      editorInstance.value.disableReadOnlyMode('disabled')
    }
  }
})
onMounted(() => {
  isLayoutReady.value = true
})
</script>

<template>
  <div :id="editorId" class="yw-editor w-100%">
    <Ckeditor
      v-if="editor && config"
      v-model="editorValue"
      class="question-item"
      :style="{ '--min-height': `${props.minHeight}px` }"
      :editor="editor"
      :config="config"
      @ready="handleEditorReady"
      @error="handleEditorError"
    />
  </div>
</template>

<style scoped>
/* CKEditor 全局配置 */
:global(:root) {
  /* 工具栏层级设置 */
  --ck-z-panel: 9999;
}

/* 隐藏 CKEditor 的 powered by 标识 */
:global(.ck.ck-balloon-panel.ck-powered-by-balloon) {
  display: none !important;
}

/* 编辑器容器样式 */
.yw-editor {
  width: 100%;
}

/* 编辑器主体样式 */
.question-item {
  flex: 1;
  background-color: #fff;
  border: 1px solid var(--border, #d9d9d9);
  border-radius: 4px;
  box-sizing: border-box;
  color: #333;
  margin-left: 8px;
  min-height: var(--min-height, 32px);
  padding: 3px 8px;
  word-break: break-word;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

/* 禁用状态样式 */
.question-item.is-disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 段落样式重置 */
.question-item :deep(p) {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

/* CKEditor 内联编辑器样式覆盖 */
:global(.ck.ck-editor__editable_inline) {
  border: 1px solid #d9d9d9 !important;
  padding: 3px 8px !important;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out !important;
}

/* 聚焦状态样式 */
:global(.ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable)) {
  border: 1px solid #386bff !important;
  box-shadow: inset 2px 2px 3px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}

/* 数学公式样式 */
:global(.math-tex) {
  background-color: #f0f8ff;
  border-radius: 3px;
  padding: 2px 4px;
  margin: 0 2px;
  font-family: 'Times New Roman', serif;
}
</style>
